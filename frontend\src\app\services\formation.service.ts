import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseApiService } from './base-api.service';
import { 
  Formation, 
  FormationCreateDTO, 
  FormationUpdateDTO, 
  FormationResponseDTO 
} from '../models/formation.model';

@Injectable({
  providedIn: 'root'
})
export class FormationService extends BaseApiService {

  getAllFormations(): Observable<FormationResponseDTO[]> {
    return this.get<FormationResponseDTO[]>('/formations');
  }

  getFormationById(id: number): Observable<FormationResponseDTO> {
    return this.get<FormationResponseDTO>(`/formations/${id}`);
  }

  createFormation(formation: FormationCreateDTO): Observable<FormationResponseDTO> {
    return this.post<FormationResponseDTO>('/formations', formation);
  }

  updateFormation(id: number, formation: FormationUpdateDTO): Observable<FormationResponseDTO> {
    return this.put<FormationResponseDTO>(`/formations/${id}`, formation);
  }

  deleteFormation(id: number): Observable<any> {
    return this.delete(`/formations/${id}`);
  }

  searchFormations(keyword?: string): Observable<FormationResponseDTO[]> {
    const endpoint = keyword ? `/formations/search?keyword=${encodeURIComponent(keyword)}` : '/formations/search';
    return this.get<FormationResponseDTO[]>(endpoint);
  }

  getFormationModules(formationId: number): Observable<any[]> {
    return this.get<any[]>(`/formations/${formationId}/modules`);
  }

  getModuleNiveaux(formationId: number, moduleId: number): Observable<any[]> {
    return this.get<any[]>(`/formations/${formationId}/modules/${moduleId}/niveaux`);
  }
}
