import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { BaseApiService } from './base-api.service';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  AddUserDto, 
  ChangePasswordDto 
} from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class UserService extends BaseApiService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    super();
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing user from localStorage:', error);
        localStorage.removeItem('user');
      }
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.post<LoginResponse>('/users/login', credentials)
      .pipe(
        tap(response => {
          if (response.success && response.token) {
            localStorage.setItem('token', response.token);
            localStorage.setItem('user', JSON.stringify(response.user));
            this.currentUserSubject.next(response.user);
          }
        })
      );
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    this.currentUserSubject.next(null);
  }

  isLoggedIn(): boolean {
    return !!localStorage.getItem('token');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  addStudent(studentData: AddUserDto): Observable<any> {
    return this.post('/users/add-student', studentData);
  }

  addProfessor(professorData: AddUserDto): Observable<any> {
    return this.post('/users/add-professor', professorData);
  }

  changePassword(passwordData: ChangePasswordDto): Observable<any> {
    return this.post('/users/change-password', passwordData);
  }

  getAllUsers(): Observable<User[]> {
    return this.get<User[]>('/users');
  }

  getUserById(id: number): Observable<User> {
    return this.get<User>(`/users/${id}`);
  }

  updateUser(id: number, userData: Partial<User>): Observable<User> {
    return this.put<User>(`/users/${id}`, userData);
  }

  deleteUser(id: number): Observable<any> {
    return this.delete(`/users/${id}`);
  }
}
